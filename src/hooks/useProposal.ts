"use client";

import use<PERSON><PERSON> from "swr";

import { useRouter, useParams } from "next/navigation";

import { fetcher } from "@/lib/common/requests";
import { toast } from "sonner";
import { useCallback, useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import {
  fetchProposal,
  createProposal,
  updateProposal,
  deleteProposal,
  searchProposals,
} from "@/store/actions/proposals";
import {
  selectCurrentProposal,
  selectIsCreating,
  selectIsUpdating,
  selectIsDeleting,
  selectError,
  clearError,
  setCreating,
  setUpdating,
  setDeleting,
  setError,
  setCurrentProposal,
} from "@/store/slices/proposal";
import type {
  CreateProposal,
  UpdateProposal,
  Proposal as ApiProposal,
} from "@/lib/api/validators/schemas/proposal";
import type { Proposal, ProposalStats } from "@/data/proposals-mock";
import { usePagination } from "./usePagination";
import { createSearchFunction, type SearchConfig } from "@/lib/search";

// Convert API Proposal to UI Proposal for display
const adaptApiProposalToUI = (apiProposal: ApiProposal): Proposal => {
  const statusMap: Record<string, Proposal["status"]> = {
    created: "draft",
    pending: "pending",
    approved: "approved",
    rejected: "rejected",
    completed: "completed",
  };

  return {
    id: apiProposal.id,
    name: apiProposal.name,
    description: apiProposal.description || "",
    client: apiProposal.client?.name || "",
    status: statusMap[apiProposal.status] || "draft",
    budgetType: apiProposal.fixed_budget ? "fixed" : "milestone",
    fixedBudget: apiProposal.fixed_budget,
    milestones: apiProposal.milestones || [],
    totalBudget: apiProposal.total_budget || 0,
    duration: apiProposal.duration || 0,
    createdDate: new Date(apiProposal.createdAt)?.toISOString(),
    lastModified: new Date(apiProposal.updatedAt)?.toISOString(),
    attachments: apiProposal.links || [],
    agreed_to_terms_and_conditions: apiProposal.agreed_to_terms_and_conditions,
  };
};

// Convert UI Proposal to API Proposal for submission
const adaptUIProposalToAPI = (uiProposal: Proposal): Partial<ApiProposal> => {
  const statusMap: Record<Proposal["status"], ApiProposal["status"]> = {
    draft: "created",
    pending: "submitted",
    approved: "agreed",
    rejected: "closed",
    completed: "completed",
  };

  return {
    id: uiProposal.id,
    name: uiProposal.name,
    description: uiProposal.description,
    status: statusMap[uiProposal.status] || "created",
    links: uiProposal.attachments || [],
    milestones: uiProposal.milestones || [],
    fixed_budget: uiProposal.fixedBudget,
    total_budget: uiProposal.totalBudget || 0,
    duration: uiProposal.duration || 0,
    agreed_to_terms_and_conditions: uiProposal.agreed_to_terms_and_conditions,
  };
};

export function useProposal() {
  const router = useRouter();
  const { slug } = useParams();

  const dispatch = useDispatch<AppDispatch>();

  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [filteredProposals, setFilteredProposals] = useState<ApiProposal[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);

  // Initialize pagination hook for proposals table
  const paginationHook = usePagination("proposals-table");

  // Redux selectors for operation states
  const currentProposal = useSelector((state: RootState) =>
    selectCurrentProposal(state)
  );
  const isCreating = useSelector((state: RootState) => selectIsCreating(state));
  const isUpdating = useSelector((state: RootState) => selectIsUpdating(state));
  const isDeleting = useSelector((state: RootState) => selectIsDeleting(state));
  const error = useSelector((state: RootState) => selectError(state));

  // SWR hooks for data fetching
  const {
    data: proposalsData,
    error: proposalsError,
    isLoading: isLoadingProposals,
    mutate: mutateProposals,
  } = useSWR("proposal", fetcher);

  const {
    data: statisticsData,
    error: statisticsError,
    isLoading: isLoadingStats,
    mutate: mutateStatistics,
  } = useSWR("proposal/statistics", fetcher);

  // Extract data from SWR responses
  const serverPagination = proposalsData?.pagination;
  const statistics: ProposalStats | null = statisticsData?.data || null;
  const isLoading = isLoadingProposals;

  // Helper function to adapt API proposals to UI format
  function setUiProposals(apiProposals: ApiProposal[]) {
    if (!apiProposals || apiProposals.length === 0) return;
    const uiProposals = apiProposals.map(adaptApiProposalToUI);
    setProposals(uiProposals);
  }

  useEffect(() => {
    // Reset Proposals Data on search term clear
    if (!searchTerm) {
      setUiProposals(proposalsData?.data);
    }
  }, [searchTerm]);

  useEffect(() => {
    if (filteredProposals) {
      setUiProposals(filteredProposals);
    }
  }, [filteredProposals]);

  useEffect(() => {
    if (proposalsData) {
      setUiProposals(proposalsData.data);
    }
  }, [proposalsData]);

  // Initialize and update pagination based on fetched data
  useEffect(() => {
    if (proposalsData) {
      // Initialize pagination if not already done
      if (paginationHook.pagination.total === 0) {
        paginationHook.initPagination({
          initialPage: serverPagination?.page || 1,
          initialLimit: serverPagination?.limit || 10,
          initialOrderBy: { createdAt: "desc" },
        });
      }

      // Update pagination with server data when available
      if (serverPagination) {
        paginationHook.updateTotal(serverPagination.total);
        // Update other pagination metadata if needed
        if (serverPagination.page !== paginationHook.pagination.page) {
          paginationHook.setPage(serverPagination.page);
        }
        if (serverPagination.limit !== paginationHook.pagination.limit) {
          paginationHook.setLimit(serverPagination.limit);
        }
      }
    }
  }, [proposalsData, serverPagination, paginationHook]);

  // Handle SWR errors
  const swrError = proposalsError || statisticsError;
  if (swrError && !error) {
    dispatch(setError(swrError.message));
  }

  // API operations
  const create = useCallback(
    async (data: CreateProposal) => {
      try {
        dispatch(setCreating(true));
        dispatch(clearError());

        const result = await dispatch(createProposal(data)).unwrap();

        // Show success toast
        toast.success("Proposal created successfully");

        // Refresh data after creation
        await mutateProposals();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create proposal";
        toast.error(errorMessage);
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setCreating(false));
      }
    },
    [dispatch, mutateProposals, mutateStatistics]
  );

  const update = useCallback(
    async (data: UpdateProposal) => {
      try {
        dispatch(setUpdating(true));
        dispatch(clearError());

        const result = await dispatch(updateProposal(data)).unwrap();

        // Show success toast
        toast.success("Proposal updated successfully");

        // Update current proposal if it's the one being updated
        if (currentProposal?.id === data.id) {
          dispatch(setCurrentProposal(result));
        }

        // Refresh data after update
        await mutateProposals();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update proposal";
        toast.error(errorMessage);
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setUpdating(false));
      }
    },
    [dispatch, currentProposal, mutateProposals, mutateStatistics]
  );

  const remove = useCallback(
    async (id: string) => {
      try {
        dispatch(setDeleting(true));
        dispatch(clearError());

        await dispatch(deleteProposal(id)).unwrap();

        // Show success toast
        toast.success("Proposal deleted successfully");

        router.push(`/${slug}/proposals`);

        // Clear current proposal if it's the one being deleted
        if (currentProposal?.id === id) {
          dispatch(setCurrentProposal(null));
        }

        // Refresh data after deletion
        await mutateProposals();
        await mutateStatistics();
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete proposal";
        toast.error(errorMessage);
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setDeleting(false));
      }
    },
    [dispatch, currentProposal, mutateProposals, mutateStatistics]
  );

  // Utility functions
  const fetchById = useCallback(
    async (id: string) => {
      try {
        const result = await dispatch(fetchProposal(id)).unwrap();
        dispatch(setCurrentProposal(result));
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch proposal";
        dispatch(setError(errorMessage));
        throw error;
      }
    },
    [dispatch]
  );

  // Search configuration for the composable search function
  const searchConfig: SearchConfig<Proposal, ApiProposal> = {
    adaptApiToUI: adaptApiProposalToUI,
    searchFields: (proposal: ApiProposal) => [
      proposal.name || "",
      proposal.description || "",
      proposal.status || "",
      proposal.account?.user?.name || "",
      proposal.account?.user?.email || "",
      proposal.total_budget?.toString() || "",
    ],
    setFilteredData: setFilteredProposals,
    setSearchTerm,
    setIsSearching,
    searchAction: searchProposals,
    currentData: proposals,
    loadedApiData: proposalsData?.data,
    debounceDelay: 1400,
  };

  // Create the composable search function
  const { searchFunction, cleanup } = createSearchFunction(
    dispatch,
    searchConfig
  );

  // Search Proposals (using composable function)
  const searchProposalsFunction = useCallback(
    async (searchParams: { query?: string; page?: number; limit?: number }) => {
      return await searchFunction(searchParams);
    },
    [searchFunction]
  );

  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Refresh data functions using SWR mutate
  const refreshProposals = useCallback(() => {
    mutateProposals();
  }, [mutateProposals]);

  const refreshStatistics = useCallback(() => {
    mutateStatistics();
  }, [mutateStatistics]);

  const refreshData = useCallback(() => {
    mutateProposals();
    mutateStatistics();
  }, [mutateProposals, mutateStatistics]);

  // Initialize proposals data using SWR
  const initializeProposals = useCallback(() => {
    refreshData();
  }, [refreshData]);

  // Cleanup search function on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    // State
    proposals,
    currentProposal,
    statistics,
    searchTerm,
    isLoading,
    isSearching,
    isCreating,
    isUpdating,
    isDeleting,
    isLoadingStats,
    error,

    // Pagination
    pagination: paginationHook,

    // Data refresh actions (SWR-based)
    refreshProposals,
    refreshStatistics,
    refreshData,

    // Actions
    create,
    fetchById,
    update,
    remove,
    searchProposals: searchProposalsFunction,
    initializeProposals,
    setSearchTerm,
    clearError: clearErrorState,

    // Data transformation utilities
    adaptUIProposalToAPI,

    // SWR utilities
    mutateProposals,
    mutateStatistics,

    // Computed values
    hasProposals: proposals.length > 0,
    isAnyLoading:
      isLoading || isCreating || isUpdating || isDeleting || isLoadingStats,
  };
}
