"use client";

import { redirect } from "next/navigation";
import { useSelector, useDispatch } from "react-redux";
import { useSession } from "next-auth/react";
import { RootState, AppDispatch } from "@/store";
import { toast } from "sonner";
import {
  refreshSession,
  loginUserWithOAuth,
  loginUserWithCredentials,
  registerUserWithCredentials,
  logoutUser,
  updateUserAvatar,
} from "@/store/actions/auth";
import {
  selectAuth,
  selectRole,
  selectSession,
  selectUserAccount,
  selectSessionAccount,
  selectPermissions,
  selectUser,
  selectIsAuthenticated,
  selectIsLoading,
  selectIsDashboardView,
  updateUser,
  setDashboardView,
  toggleDashboardView,
  selectProfile,
} from "@/store/slices/auth";

export function useAuth() {
  const dispatch = useDispatch<AppDispatch>();
  const { data: nextAuthSession } = useSession();

  // Redux selectors
  const auth = useSelector(selectAuth);
  const user = useSelector(selectUser);
  const role = useSelector(selectRole);
  const session = useSelector(selectSession);
  const profile = useSelector(selectProfile);
  const permissions = useSelector(selectPermissions);

  const userAccount = useSelector(selectUserAccount);
  const sessionAccount = useSelector(selectSessionAccount);

  const accountId: string = userAccount?.id || sessionAccount?.id || "";

  // Auth state
  const { data: currentSession, update: updateCurrentSession } = useSession();
  const isAuthenticated = useSelector((state: RootState) =>
    selectIsAuthenticated(state)
  );
  const isLoading = useSelector((state: RootState) => selectIsLoading(state));
  const isDashboardView = useSelector((state: RootState) =>
    selectIsDashboardView(state)
  );

  const registerUser = async (
    name: string,
    email: string,
    password: string
  ) => {
    try {
      const result = await dispatch(
        registerUserWithCredentials({ name, email, password })
      ).unwrap();

      // Handle toast notifications based on result
      toast.success(
        result.payload?.message || "Registration successful! Please sign in."
      );
      return result;
    } catch (error) {
      console.error("Registration error:", error);
      toast.error("Registration failed");
      throw error;
    }
  };

  // Auth actions
  const loginWithOAuth = async (provider: string = "google") => {
    try {
      const result = await dispatch(loginUserWithOAuth(provider)).unwrap();

      // Handle toast notifications based on result
      toast.success(`Successfully signed in with ${provider}!`);
      setDashboardViewState(true);
      redirect("/dashboard");
    } catch (error) {
      console.error("Login error:", error);
      toast.error("OAuth login failed");
      throw error;
    }
  };

  const loginWithCredentials = async (email: string, password: string) => {
    try {
      const result = await dispatch(
        loginUserWithCredentials({ email, password })
      ).unwrap();

      // Handle toast notifications based on result
      toast.success("Login successful!");
      setDashboardViewState(true);
      redirect("/dashboard");
    } catch (error) {
      console.error("Login error:", error);
      toast.error("Login failed");
      throw error;
    }
  };

  const logout = async () => {
    try {
      const result = await dispatch(logoutUser()).unwrap();

      // Handle toast notifications based on result
      toast.success("Successfully signed out!");

      return result;
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Logout failed");
      throw error;
    }
  };

  const exitDashboardView = () => {
    setDashboardViewState(false);
    redirect("/");
  };

  const refreshUserSession = async () => {
    if (isAuthenticated) return;

    try {
      const result = await dispatch(refreshSession());
      return result;
    } catch (error) {
      console.error("Refresh session error:", error);
      throw error;
    }
  };

  const updateUserProfile = (userData: Partial<typeof user>) => {
    dispatch(updateUser(userData));
  };

  const updateAvatar = async (avatar: File) => {
    if (!user?.id) return;

    let formData = new FormData();
    formData.append("avatar", avatar, avatar.name);

    try {
      const result = await dispatch(
        updateUserAvatar({ id: user?.id, avatar: formData })
      ).unwrap();

      await updateCurrentSession({
        ...currentSession,
        image: result.image,
      });

      // Handle toast notifications based on result
      toast.success("Profile updated successfully");
      return result;
    } catch (error) {
      console.error("Update avatar error:", error);
      toast.error("Failed to update avatar");
      throw error;
    }
  };

  function routeAuthenticated() {
    if (!isAuthenticated) redirect("/signin");

    setDashboardViewState(true);
    redirect("/dashboard");
  }

  // Dashboard view actions
  const setDashboardViewState = (isDashboard: boolean) => {
    dispatch(setDashboardView(isDashboard));
  };

  const toggleDashboardViewState = () => {
    dispatch(toggleDashboardView());
  };

  const isCurrentSessionYours = (id: string): boolean =>
    id ? id === accountId : false;

  return {
    // State
    auth,
    user,
    role,
    session,
    profile,
    isLoading,
    accountId,
    permissions,
    isAuthenticated,
    isDashboardView,
    nextAuthSession,

    // Actions
    logout,
    updateAvatar,
    registerUser,
    loginWithOAuth,
    updateUserProfile,
    exitDashboardView,
    routeAuthenticated,
    refreshUserSession,
    loginWithCredentials,
    setDashboardViewState,
    isCurrentSessionYours,
    toggleDashboardViewState,
  };
}
