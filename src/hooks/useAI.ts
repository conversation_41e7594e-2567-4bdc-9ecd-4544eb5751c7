/**
 * React Hook for AI Model Integration
 *
 * This hook provides a simple interface for interacting with the AI library
 * from React components.
 */

import { useState, useCallback } from "react";

interface AIResponse {
  content: string;
  model: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  timestamp: string;
  mock?: boolean;
}

interface AIError {
  error: string;
  message?: string;
}

interface UseAIOptions {
  model?: string;
  systemPrompt?: string;
  temperature?: number;
  maxTokens?: number;
  onStart?: () => void;
  onComplete?: (response: string) => void;
  onError?: (error: AIError) => void;
}

interface UseAIReturn {
  // State
  isLoading: boolean;
  response: AIResponse | null;
  error: AIError | null;

  // Actions
  generateResponse: (prompt: string, options?: UseAIOptions) => Promise<void>;
  streamResponse: (prompt: string, options?: UseAIOptions) => Promise<void>;
  clearResponse: () => void;

  // Streaming state
  isStreaming: boolean;
  streamContent: string;
}

/**
 * Hook for AI model interactions
 */
export function useAI(defaultOptions: UseAIOptions = {}): UseAIReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [response, setResponse] = useState<AIResponse | null>(null);
  const [error, setError] = useState<AIError | null>(null);
  const [streamContent, setStreamContent] = useState("");

  const clearResponse = useCallback(() => {
    setResponse(null);
    setError(null);
    setStreamContent("");
  }, []);

  const generateResponse = useCallback(
    async (prompt: string, options: UseAIOptions = {}) => {
      const mergedOptions = { ...defaultOptions, ...options };

      setIsLoading(true);
      setError(null);
      setResponse(null);

      if (mergedOptions.onStart) {
        mergedOptions.onStart();
      }

      try {
        const response = await fetch("/api/ai/chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            prompt,
            model: mergedOptions.model,
            systemPrompt: mergedOptions.systemPrompt,
            temperature: mergedOptions.temperature,
            maxTokens: mergedOptions.maxTokens,
            stream: false,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to generate response");
        }

        const data = await response.json();

        if (data.success) {
          const aiResponse: AIResponse = {
            content: data.response,
            model: data.model,
            usage: data.usage,
            timestamp: data.timestamp,
            mock: data.mock,
          };

          setResponse(aiResponse);

          if (mergedOptions.onComplete) {
            mergedOptions.onComplete(data.response);
          }
        } else {
          throw new Error(data.error || "Unknown error");
        }
      } catch (err) {
        const aiError: AIError = {
          error: err instanceof Error ? err.message : "Unknown error",
          message: "Failed to generate AI response",
        };

        setError(aiError);

        if (mergedOptions.onError) {
          mergedOptions.onError(aiError);
        }
      } finally {
        setIsLoading(false);
      }
    },
    [defaultOptions]
  );

  const streamResponse = useCallback(
    async (prompt: string, options: UseAIOptions = {}) => {
      const mergedOptions = { ...defaultOptions, ...options };

      setIsStreaming(true);
      setError(null);
      setResponse(null);
      setStreamContent("");

      if (mergedOptions.onStart) {
        mergedOptions.onStart();
      }

      try {
        const response = await fetch("/api/ai/chat", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            prompt,
            model: mergedOptions.model,
            systemPrompt: mergedOptions.systemPrompt,
            temperature: mergedOptions.temperature,
            maxTokens: mergedOptions.maxTokens,
            stream: true,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to stream response");
        }

        const reader = response.body?.getReader();
        if (!reader) {
          throw new Error("No response body");
        }

        const decoder = new TextDecoder();
        let fullContent = "";

        while (true) {
          const { done, value } = await reader.read();

          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split("\n").filter((line) => line.trim());

          for (const line of lines) {
            try {
              const data = JSON.parse(line);
              if (data.content) {
                fullContent = data.content;
                setStreamContent(fullContent);
              }
            } catch (parseError) {
              // Ignore parsing errors for partial chunks
            }
          }
        }

        // Set final response
        const aiResponse: AIResponse = {
          content: fullContent,
          model: mergedOptions.model || "claude-3-5-sonnet-4.1",
          timestamp: new Date().toISOString(),
        };

        setResponse(aiResponse);

        if (mergedOptions.onComplete) {
          mergedOptions.onComplete(fullContent);
        }
      } catch (err) {
        const aiError: AIError = {
          error: err instanceof Error ? err.message : "Unknown error",
          message: "Failed to stream AI response",
        };

        setError(aiError);

        if (mergedOptions.onError) {
          mergedOptions.onError(aiError);
        }
      } finally {
        setIsStreaming(false);
      }
    },
    [defaultOptions]
  );

  return {
    // State
    isLoading,
    response,
    error,

    // Actions
    generateResponse,
    streamResponse,
    clearResponse,

    // Streaming state
    isStreaming,
    streamContent,
  };
}
