"use client";

import React, { Suspense, use } from "react";
import { ContractDetailsContainer } from "@/components/view/contracts/details/container";

interface ContractDetailsPageProps
  extends Promise<{ id: string; slug: string }> {
  id: string;
  slug: string;
}

export default function ContractDetailsPage({
  params,
}: {
  params: Promise<ContractDetailsPageProps>;
}) {
  const contract: ContractDetailsPageProps = use(params);

  return (
    <Suspense fallback={<div>Loading contract details...</div>}>
      <ContractDetailsContainer contractId={contract?.id} />
    </Suspense>
  );
}
