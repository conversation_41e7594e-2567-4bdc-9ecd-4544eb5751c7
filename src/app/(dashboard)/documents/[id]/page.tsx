"use client";

import React, { Suspense, use } from "react";
import { DocumentDetailsContainer } from "@/components/view/documents/details/container";

interface DocumentDetailsPageProps {
  params: {
    slug: string;
    id: string;
  };
}

export default function DocumentDetailsPage({
  params,
}: {
  params: Promise<DocumentDetailsPageProps["params"]>;
}) {
  const documentParams = use(params);
  return (
    <Suspense fallback={<div>Loading document details...</div>}>
      <DocumentDetailsContainer documentId={documentParams?.id} />
    </Suspense>
  );
}
