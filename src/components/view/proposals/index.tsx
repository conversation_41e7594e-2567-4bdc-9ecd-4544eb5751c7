"use client";

import { useState, useEffect, useCallback } from "react";
import { useParams } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { useProposal } from "@/hooks/useProposal";
import { useLayout } from "@/hooks/useLayout";
import { CanR<PERSON>, RBACWrapper } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import { Listing } from "@/layouts/dashboard/details";
import { Button } from "@/components/common/ui/button";
import { type Proposal } from "@/components/common/types";
import type { Proposal as ApiProposal } from "@/lib/api/validators/schemas/proposal";

import type { DataType } from "@/layouts/dashboard/details";
import type { InsightsWidget } from "@/layouts/dashboard/details/advanced";
import { ProposalHeader } from "./header";
import { ProposalStatistics } from "./statistics";
import { ProposalTable } from "./table";
import { CreateProposalDialog } from "./create-dialog";
import { ProposalEditDialog } from "./details/edit-dialog";
import { ProposalDeleteDialog } from "./details/delete-dialog";
import { FileText, DollarSign, TrendingUp, Clock } from "lucide-react";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";

// Main Container Component
function ProposalsContainer() {
  const { slug } = useParams();
  const { personalizedRoute } = useAuth();
  const { isAdvanced } = useLayout();

  const {
    proposals,
    statistics,
    searchTerm,
    isLoading,
    isSearching,
    error,
    create,
    update,
    remove,
    searchProposals,
    setSearchTerm,
    clearError,
    initializeProposals,
    adaptUIProposalToAPI,
  } = useProposal();

  const [showIsCreateDialog, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProposal, setSelectedProposal] = useState<Proposal | null>(
    null
  );
  // View mode state management (similar to contracts)
  const [basicViewMode, setBasicViewMode] = useState<"pages" | "flow">("pages");
  const [advancedViewMode, setAdvancedViewMode] = useState<
    "table" | "grid" | "list" | "flow"
  >("table");
  const [controlsViewMode, setControlsViewMode] = useState<"table" | "cards">(
    "table"
  );
  const [categoryFilter, setCategoryFilter] = useState<string>("all");

  // Combined loading state for table
  const isTableLoading = isLoading || isSearching;

  // Handle search functionality with immediate UI update
  const handleSearch = useCallback(
    (query: string) => {
      // Update search term immediately for UI responsiveness
      setSearchTerm(query);

      // The hook's searchProposals function handles debouncing internally
      if (query.trim() === "") {
        // Clear search and show all proposals
        return;
      }

      // Trigger search - the hook handles debouncing and API calls
      searchProposals({ query: query.trim() }).catch((error) => {
        console.error("Search failed:", error);
      });
    },
    [searchProposals, setSearchTerm]
  );

  // Handle refresh functionality
  const handleRefresh = useCallback(() => {
    // Clear search term and refresh data
    setSearchTerm("");
    initializeProposals();
  }, [setSearchTerm, initializeProposals]);

  useEffect(() => {
    initializeProposals();
  }, [slug, initializeProposals]);

  useEffect(() => {
    if (error) {
      console.error("Proposal error:", error);
    }
  }, [error]);

  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  function handleSearchCommit(value: string) {
    searchProposals({ query: value });
  }

  const handleCreateProposal = async (formData: any) => {
    try {
      await create(formData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Failed to create proposal:", error);
    }
  };

  const handleViewProposal = (proposal: Proposal) => {
    // Navigate to proposal details
    window.location.href = `/${slug}/proposals/${proposal.id}`;
  };

  // Helper functions for dialog management
  const handleEditProposalDialog = (proposal: Proposal) => {
    // Store UI proposal for editing (dialog will handle API conversion on submit)
    setSelectedProposal(proposal);
    setIsEditDialogOpen(true);
  };

  const handleDeleteProposalDialog = (proposal: Proposal) => {
    // Store UI proposal for deletion (will convert to API format when needed)
    setSelectedProposal(proposal);
    setIsDeleteDialogOpen(true);
  };

  const handleEditSubmit = async (data: Partial<ApiProposal>) => {
    if (selectedProposal) {
      try {
        await update({ id: selectedProposal.id, ...data });
        setIsEditDialogOpen(false);
        setSelectedProposal(null);
      } catch (error) {
        console.error("Failed to update proposal:", error);
      }
    }
  };

  const handleDeleteConfirm = async () => {
    if (selectedProposal) {
      try {
        await remove(selectedProposal.id);
        setIsDeleteDialogOpen(false);
        setSelectedProposal(null);
      } catch (error) {
        console.error("Failed to delete proposal:", error);
      }
    }
  };

  // Function to determine data type for dynamic icons (advanced layout)
  const getDataType = (item: Proposal, columnKey: string): DataType => {
    if (columnKey === "name") {
      return "document";
    }
    // Reference item to avoid unused parameter warning
    return item ? "default" : "default";
  };

  // Filter proposals based on category selection
  const filteredProposals =
    categoryFilter === "all"
      ? proposals
      : proposals.filter((proposal) => proposal.status === categoryFilter);

  // Custom card renderer for proposals (omits description)
  const renderProposalCard = (proposal: Proposal, index: number) => {
    return (
      <Card className="shadow-sm hover:shadow-lg transition-all duration-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium">
            {proposal.name || `Proposal ${index + 1}`}
          </CardTitle>
          {/* Intentionally omitting description */}
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">Client:</span>
              <span className="font-medium">{proposal.client}</span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">Status:</span>
              <span className="font-medium capitalize">{proposal.status}</span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">Budget:</span>
              <span className="font-medium">
                ${proposal.totalBudget?.toLocaleString() || "0"}
              </span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleViewProposal(proposal)}
            className="w-full"
          >
            <LottieIconPlayer
              icon={LottieIconLib.redirect}
              size={16}
              className="mr-2"
            />
            Open
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Categories for filtering based on proposal status
  const categories = [
    {
      key: "draft",
      label: "Draft",
      count: proposals.filter((p) => p.status === "draft").length,
    },
    {
      key: "pending",
      label: "Pending",
      count: proposals.filter((p) => p.status === "pending").length,
    },
    {
      key: "approved",
      label: "Approved",
      count: proposals.filter((p) => p.status === "approved").length,
    },
    {
      key: "completed",
      label: "Completed",
      count: proposals.filter((p) => p.status === "completed").length,
    },
  ];

  // Quick filters for advanced layout
  const quickFilters = [
    {
      label: "All Proposals",
      value: "all",
      count: proposals.length,
      active: searchTerm === "",
      onClick: () => setSearchTerm(""),
    },
    {
      label: "Draft",
      value: "draft",
      count: proposals.filter(
        (proposal: Proposal) => proposal.status === "draft"
      ).length,
      active: false,
      onClick: () => setSearchTerm("draft"),
    },
    {
      label: "Pending",
      value: "pending",
      count: proposals.filter(
        (proposal: Proposal) => proposal.status === "pending"
      ).length,
      active: false,
      onClick: () => setSearchTerm("pending"),
    },
    {
      label: "Approved",
      value: "approved",
      count: proposals.filter(
        (proposal: Proposal) => proposal.status === "approved"
      ).length,
      active: false,
      onClick: () => setSearchTerm("approved"),
    },
    {
      label: "Completed",
      value: "completed",
      count: proposals.filter(
        (proposal: Proposal) => proposal.status === "completed"
      ).length,
      active: false,
      onClick: () => setSearchTerm("completed"),
    },
  ];

  // Insights widgets for advanced layout
  const insightsWidgets: InsightsWidget[] = [
    {
      id: "total-proposals",
      title: "Total Proposals",
      value: statistics?.total || 0,
      description: "All proposals in the system",
      icon: <FileText size={16} className="text-blue-500" />,
      trend: {
        value: 15,
        isPositive: true,
      },
    },
    {
      id: "pending-proposals",
      title: "Pending Proposals",
      value: statistics?.pending || 0,
      description: "Awaiting client response",
      icon: <Clock size={16} className="text-orange-500" />,
      trend: {
        value: 5,
        isPositive: false,
      },
    },
    {
      id: "approved-proposals",
      title: "Approved Proposals",
      value: statistics?.approved || 0,
      description: "Approved by clients",
      icon: <TrendingUp size={16} className="text-green-500" />,
      trend: {
        value: 8,
        isPositive: true,
      },
    },
    {
      id: "total-value",
      title: "Total Value",
      value: statistics?.totalValue
        ? `$${statistics.totalValue.toLocaleString()}`
        : "$0",
      description: "Combined proposal value",
      icon: <DollarSign size={16} className="text-purple-500" />,
    },
  ];

  // Context menu actions for advanced layout
  const contextMenuActions = [
    {
      id: "open",
      label: "Open",
      icon: <LottieIconPlayer icon={LottieIconLib.view} size={16} />,
      onClick: (item: Proposal) => handleViewProposal(item),
    },
    {
      id: "edit",
      label: "Edit",
      icon: <LottieIconPlayer icon={LottieIconLib.edit} size={16} />,
      onClick: (item: Proposal) => handleEditProposalDialog(item),
    },
    {
      id: "separator1",
      label: "",
      separator: true,
      onClick: () => {},
    },
    {
      id: "delete",
      label: "Delete",
      icon: (
        <LottieIconPlayer
          icon={LottieIconLib.delete}
          size={16}
          className="text-red-500"
        />
      ),
      onClick: (item: Proposal) => handleDeleteProposalDialog(item),
    },
  ];

  // Table columns configuration
  const columns = [
    {
      key: "name",
      label: "Proposal",
      dataType: "document" as DataType,
    },
    {
      key: "client",
      label: "Client",
    },
    {
      key: "status",
      label: "Status",
    },
    {
      key: "totalBudget",
      label: "Budget",
      render: (item: Proposal) => `$${item.totalBudget.toLocaleString()}`,
    },
    {
      key: "duration",
      label: "Duration",
      render: (item: Proposal) => `${item.duration} days`,
    },
    {
      key: "createdDate",
      label: "Created",
      render: (item: Proposal) =>
        new Date(item.createdDate).toLocaleDateString(),
    },
  ];

  return (
    <CanRead entity={DEFAULT_ENTITIES.PROPOSAL} resourceId={personalizedRoute}>
      <Listing
        className="space-y-3"
        toolbar={
          isAdvanced
            ? {
                searchValue: searchTerm,
                onSearchChange: handleSearch,
                onSearchCommit: handleSearchCommit,
                searchPlaceholder: "Search proposals...",
                quickFilters,
                viewMode: advancedViewMode,
                onViewModeChange: setAdvancedViewMode,
                enabledViewModes: ["table", "grid", "flow"],
                showInsightsButton: true,
                insightsWidgets,
              }
            : undefined
        }
      >
        {/* Basic Layout Components */}
        {!isAdvanced && (
          <>
            <ProposalHeader
              title="Proposals"
              subtitle={`Manage your client proposals, ${proposals.length} ${
                proposals.length === 1 ? "proposal" : "proposals"
              }`}
              onCreateProposal={() => setIsCreateDialogOpen(true)}
            />

            <ProposalStatistics statistics={statistics} isLoading={isLoading} />

            <Listing.Filters
              searchTerm={searchTerm}
              onSearchChange={handleSearch}
              enableDynamicFilters={true}
              columns={columns.map((col) => ({
                key: col.key,
                label: col.label,
                searchable: true,
                type:
                  col.key === "totalBudget"
                    ? "number"
                    : col.key === "duration"
                    ? "number"
                    : col.key === "createdDate"
                    ? "date"
                    : "string",
              }))}
              tableData={filteredProposals}
              enableControls={true}
              controlsViewMode={controlsViewMode}
              onControlsViewModeChange={setControlsViewMode}
              categoryFilter={categoryFilter}
              onCategoryFilterChange={setCategoryFilter}
              categories={categories}
              defaultFilterColumn="name"
              autoSelectDefaultColumn={true}
              onRefresh={handleRefresh}
              loading={isTableLoading}
              enableViewModeControls={true}
              viewMode={basicViewMode}
              onViewModeChange={setBasicViewMode}
              controlsActions={
                <RBACWrapper
                  entity={DEFAULT_ENTITIES.CONTRACT}
                  action={PERMISSION_ACTIONS.CREATE}
                >
                  <Button
                    variant="secondary"
                    onClick={() => setIsCreateDialogOpen(true)}
                  >
                    <LottieIconPlayer
                      icon={LottieIconLib.add}
                      size={16}
                      className="mr-2"
                    />
                    Create Proposal
                  </Button>
                </RBACWrapper>
              }
            />
          </>
        )}

        {/* Advanced Layout Header */}
        {isAdvanced && (
          <Listing.Header
            title="Proposals"
            caption={`Manage your client proposals, ${proposals.length} ${
              proposals.length === 1 ? "proposal" : "proposals"
            }`}
            actions={
              <RBACWrapper
                entity={DEFAULT_ENTITIES.PROPOSAL}
                action={PERMISSION_ACTIONS.CREATE}
              >
                <Button
                  variant="secondary"
                  onClick={() => setIsCreateDialogOpen(true)}
                >
                  <LottieIconPlayer
                    icon={LottieIconLib.add}
                    size={16}
                    className="mr-2"
                  />
                  Create New Proposal
                </Button>
              </RBACWrapper>
            }
          />
        )}

        {/* Advanced Layout - Flow View Only */}
        {isAdvanced && advancedViewMode === "flow" && (
          <Listing.Flow
            data={filteredProposals}
            loading={isTableLoading}
            getDataType={(item) => getDataType(item, "name")}
            contextMenuActions={contextMenuActions}
            emptyState={
              <div className="text-center py-12">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  No proposals
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Get started by creating your first proposal.
                </p>
              </div>
            }
          />
        )}

        {/* Unified Data Renderer - Handles Table/Cards switching globally */}
        {((isAdvanced && advancedViewMode !== "flow") || !isAdvanced) && (
          <Listing.Table
            id="proposals-table"
            data={filteredProposals}
            columns={columns}
            loading={isTableLoading}
            viewMode={basicViewMode}
            displayMode={
              isAdvanced
                ? advancedViewMode === "grid"
                  ? "cards"
                  : "table"
                : controlsViewMode
            }
            getDataType={getDataType}
            renderCard={renderProposalCard}
            contextMenuActions={contextMenuActions}
            emptyState={
              <div className="text-center py-12">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  No proposals
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Get started by creating your first proposal.
                </p>
              </div>
            }
          />
        )}

        {/* Create Proposal Dialog */}
        {showIsCreateDialog && (
          <CreateProposalDialog
            isOpen={showIsCreateDialog}
            onOpenChange={setIsCreateDialogOpen}
            onSubmit={handleCreateProposal}
            isCreating={isLoading}
            isEditing={false}
          />
        )}

        {/* Edit Proposal Dialog */}
        {selectedProposal && (
          <ProposalEditDialog
            proposal={selectedProposal}
            isOpen={isEditDialogOpen}
            onOpenChange={setIsEditDialogOpen}
            onSubmit={handleEditSubmit}
            isUpdating={isLoading}
          />
        )}

        {/* Delete Proposal Dialog */}
        {selectedProposal && (
          <ProposalDeleteDialog
            proposal={adaptUIProposalToAPI(selectedProposal) as ApiProposal}
            isOpen={isDeleteDialogOpen}
            onOpenChange={setIsDeleteDialogOpen}
            onConfirm={handleDeleteConfirm}
            isDeleting={isLoading}
          />
        )}
      </Listing>
    </CanRead>
  );
}

// Export compound component with dot notation
export const Proposals = Object.assign(ProposalsContainer, {
  Header: ProposalHeader,
  Statistics: ProposalStatistics,
  Table: ProposalTable,
  Form: CreateProposalDialog,
  EditDialog: ProposalEditDialog,
  DeleteDialog: ProposalDeleteDialog,
});
