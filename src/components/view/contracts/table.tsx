"use client";

import { useState } from "react";
import { useRouter, redirect } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Badge } from "@/components/common/ui/badge";
import { trimming } from "@/lib/common/utils";
import { useParams } from "next/navigation";
import { RBACWrapper } from "@/components/common/rbac/RBACWrapper";
import { usePermissions } from "@/hooks/useRBAC";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import { IoFolderOpenOutline as OpenFileIcon } from "react-icons/io5";
import { FileText } from "lucide-react";
import { type Contract } from "@/components/common/types";
import type { Contract as ApiContract } from "@/lib/api/validators/schemas/contract";
import HtmlParser from "react-html-parser";
import { Listing } from "@/layouts/dashboard/details/basic";

interface ContractTableProps {
  contracts: Contract[];
  isLoading: boolean;
  onUpdateContract: (id: string, data: Partial<ApiContract>) => void;
  onDeleteContract: (id: string) => void;
  viewMode?: "pages" | "flow";
}

export function ContractTable({
  contracts,
  isLoading,
  onUpdateContract,
  onDeleteContract,
  viewMode = "pages",
}: ContractTableProps) {
  const { slug } = useParams();
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const { hasEntityPermission } = usePermissions();

  const handleView = (contractId: string) => {
    redirect(`/${slug}/contracts/${contractId}`);
  };

  const handleEdit = (contractId: string) => {
    // TODO: Implement edit functionality
    console.log("Edit contract:", contractId);
  };

  const handleDelete = async (contractId: string) => {
    if (confirm("Are you sure you want to delete this contract?")) {
      setDeletingId(contractId);
      try {
        await onDeleteContract(contractId);
      } finally {
        setDeletingId(null);
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const columns = [
    {
      key: "title",
      label: "Contract Title",
      render: (contract: Contract) => (
        <div className="flex flex-col">
          <span className="font-medium text-gray-900 dark:text-white">
            {contract.title}
          </span>
          <span className="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">
            {HtmlParser(trimming(contract.description, 100))}
          </span>
        </div>
      ),
    },
    {
      key: "clientName",
      label: "Client",
      render: (contract: Contract) => (
        <span className="text-gray-900 dark:text-white">
          {contract.clientName || "N/A"}
        </span>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (contract: Contract) => {
        const statusColors = {
          draft:
            "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300",
          active:
            "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-300",
          completed:
            "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-300",
          terminated:
            "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-300",
          expired:
            "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-300",
        };

        return (
          <Badge variant="secondary" className={statusColors[contract.status]}>
            {contract.status.charAt(0).toUpperCase() + contract.status.slice(1)}
          </Badge>
        );
      },
    },
    {
      key: "contractValue",
      label: "Value",
      render: (contract: Contract) => (
        <span className="font-medium text-gray-900 dark:text-white">
          {formatCurrency(contract.contractValue)}
        </span>
      ),
    },
    {
      key: "startDate",
      label: "Start Date",
      render: (contract: Contract) => (
        <span className="text-gray-500 dark:text-gray-400">
          {formatDate(contract.startDate)}
        </span>
      ),
    },
    {
      key: "endDate",
      label: "End Date",
      render: (contract: Contract) => (
        <span className="text-gray-500 dark:text-gray-400">
          {formatDate(contract.endDate)}
        </span>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (contract: Contract) => (
        <RBACWrapper
          entity={DEFAULT_ENTITIES.CONTRACT}
          action={PERMISSION_ACTIONS.READ}
        >
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleView(contract.id)}
          >
            <OpenFileIcon />
            Open
          </Button>
        </RBACWrapper>
      ),
    },
  ];

  return (
    <Listing.Table
      id="contracts-table"
      data={contracts}
      columns={columns}
      loading={isLoading}
      viewMode={viewMode}
      emptyState={
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            No contracts
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Get started by creating a new contract.
          </p>
        </div>
      }
    />
  );
}
