"use client";

import { useState, useEffect, useCallback } from "react";
import { usePara<PERSON>, redirect } from "next/navigation";
import { useDocuments } from "@/hooks/useDocuments";
import { useLayout } from "@/hooks/useLayout";
import { Listing } from "@/layouts/dashboard/details";
import { DocumentHeader } from "./header";
import { DocumentStatistics as DocumentStatisticsComponent } from "./statistics";
import { DocumentCreateDialog } from "./create-dialog";
import { DocumentEditDialog } from "./details/edit-dialog";
import { DocumentDeleteDialog } from "./details/delete-dialog";
import { Button } from "@/components/common/ui/button";
import { RBACWrapper } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import { FileText } from "lucide-react";
import { LottieIconPlayer, LottieIconLib } from "@/components/common/lottie";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import type { Document as ApiDocument } from "@/lib/api/validators/schemas/document";
import type { Document } from "@/components/common/types";
import type { DataType } from "@/layouts/dashboard/details";
import type { InsightsWidget } from "@/layouts/dashboard/details/advanced";

// Adapter function to convert API document to UI document
const adaptApiDocumentToUI = (apiDocument: ApiDocument): Document => ({
  id: apiDocument.id,
  name: apiDocument.name,
  path: apiDocument.path,
  file_type: apiDocument.file_type,
  size: apiDocument.size,
  status: apiDocument.status as Document["status"],
  category: apiDocument.category,
  association_entity: apiDocument.association_entity,
  association_id: apiDocument.association_id,
  proposalId: apiDocument.proposalId,
  createdAt: new Date(apiDocument.createdAt).toISOString(),
  updatedAt: new Date(apiDocument.updatedAt).toISOString(),
});

export function DocumentContainer() {
  const { slug } = useParams();
  const { isAdvanced } = useLayout();

  const {
    documents,
    statistics,
    searchTerm,
    isLoading,
    isSearching,
    isAnyLoading,
    create,
    update,
    remove,
    downloadDocument,
    copyDocument,
    searchDocuments,
    setSearchTerm,
    clearError,
    mutateDocuments,
    mutateStatistics,
  } = useDocuments();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<ApiDocument | null>(
    null
  );
  // View mode state management (unified approach)
  const [basicViewMode, setBasicViewMode] = useState<"pages" | "flow">("pages");
  const [advancedViewMode, setAdvancedViewMode] = useState<
    "table" | "grid" | "list" | "flow"
  >("table");
  const [controlsViewMode, setControlsViewMode] = useState<"table" | "cards">(
    "table"
  );
  const [categoryFilter, setCategoryFilter] = useState<string>("all");

  // Combined loading state for table
  const isTableLoading = isLoading || isSearching;

  // Handle search functionality with immediate UI update
  const handleSearch = useCallback(
    (query: string) => {
      // Update search term immediately for UI responsiveness
      setSearchTerm(query);

      // The hook's searchDocuments function handles debouncing internally
      if (query.trim() === "") {
        // Clear search and show all documents
        return;
      }

      // Trigger search - the hook handles debouncing and API calls
      searchDocuments({ query: query.trim() }).catch((error) => {
        console.error("Search failed:", error);
      });
    },
    [searchDocuments, setSearchTerm]
  );

  // Handle refresh functionality
  const handleRefresh = useCallback(() => {
    // Clear search term and refresh data
    setSearchTerm("");
    mutateDocuments();
    mutateStatistics();
  }, [setSearchTerm, mutateDocuments, mutateStatistics]);

  // Handle search commit functionality (for advanced toolbar)
  function handleSearchCommit(value: string) {
    searchDocuments({ query: value });
  }

  useEffect(() => {
    if (!slug) {
      redirect("/");
    }
    // Data is automatically fetched by useSWR in the hook
    // No need for manual initialization
  }, [slug]);

  // useEffect(() => {
  //   if (error) {
  //     console.error("Document error:", error);
  //   }
  // }, [error]);

  // Clear error when component unmounts or when user interacts
  // useEffect(() => {
  //   return () => {
  //     clearError();
  //   };
  // }, [clearError]);

  const handleCreateDocument = async (documentData: FormData) => {
    try {
      await create(documentData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Failed to create document:", error);
    }
  };

  const handleUpdateDocument = async (id: string, documentData: any) => {
    try {
      await update(id, documentData);
    } catch (error) {
      console.error("Failed to update document:", error);
    }
  };

  const handleDeleteDocument = async (id: string) => {
    try {
      await remove(id);
    } catch (error) {
      console.error("Failed to delete document:", error);
    }
  };

  // Helper functions for dialog management
  const handleOpenDocument = (document: Document) => {
    // Navigate to document details or open in new tab
    window.open(`/${slug}/documents/${document.id}`, "_blank");
  };

  const handleEditDocumentDialog = (document: Document) => {
    // Convert MockDocument to Document for the dialog
    const apiDocument: ApiDocument = {
      id: document.id,
      name: document.name,
      path: document.path,
      file_type: document.file_type,
      size: document.size,
      status: document.status as Document["status"],
      category: document.category,
      association_entity: document.association_entity,
      association_id: document.association_id,
      proposalId: document.proposalId,
      createdAt: new Date(document.createdAt),
      updatedAt: new Date(document.updatedAt),
    };
    setSelectedDocument(apiDocument);
    setIsEditDialogOpen(true);
  };

  const handleEditSubmit = async (data: any) => {
    if (selectedDocument) {
      try {
        await update(selectedDocument.id, data);
        setIsEditDialogOpen(false);
        setSelectedDocument(null);
      } catch (error) {
        console.error("Failed to update document:", error);
      }
    }
  };

  const handleDeleteConfirm = async () => {
    if (selectedDocument) {
      try {
        await remove(selectedDocument.id);
        setIsDeleteDialogOpen(false);
        setSelectedDocument(null);
      } catch (error) {
        console.error("Failed to delete document:", error);
      }
    }
  };

  const handleDownloadDocument = async (document: Document) => {
    try {
      await downloadDocument(document);
    } catch (error) {
      console.error("Failed to download document:", error);
    }
  };

  const handleCopyDocument = async (document: Document) => {
    try {
      // Convert Document to ApiDocument for the API
      const apiDocument: ApiDocument = {
        id: document.id,
        name: document.name,
        path: document.path,
        file_type: document.file_type,
        size: document.size,
        status: document.status as Document["status"],
        category: document.category,
        association_entity: document.association_entity,
        association_id: document.association_id,
        proposalId: document.proposalId,
        createdAt: new Date(document.createdAt),
        updatedAt: new Date(document.updatedAt),
      };
      await copyDocument(apiDocument);
    } catch (error) {
      console.error("Failed to copy document:", error);
    }
  };

  // Convert API documents to UI documents
  const uiDocuments = documents.map(adaptApiDocumentToUI);

  // Custom card renderer for documents (omits description)
  const renderDocumentCard = (document: Document, index: number) => {
    return (
      <Card className="shadow-sm hover:shadow-lg transition-all duration-200">
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium">
            {document.name || `Document ${index + 1}`}
          </CardTitle>
          {/* Intentionally omitting description */}
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">Type:</span>
              <span className="font-medium">{document.file_type}</span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">Status:</span>
              <span className="font-medium capitalize">{document.status}</span>
            </div>
            <div className="flex justify-between items-center text-sm">
              <span className="text-muted-foreground">Size:</span>
              <span className="font-medium">{document.size}</span>
            </div>
          </div>
        </CardContent>
        <CardFooter className="pt-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleOpenDocument(document)}
            className="w-full"
          >
            <LottieIconPlayer
              icon={LottieIconLib.redirect}
              size={16}
              className="mr-2"
            />
            Open
          </Button>
        </CardFooter>
      </Card>
    );
  };

  // Function to determine data type for dynamic icons (advanced layout)
  const getDataType = (item: Document, columnKey: string): DataType => {
    if (columnKey === "name") {
      const fileType = item.file_type.toLowerCase();
      if (
        fileType.includes("image") ||
        fileType.includes("jpg") ||
        fileType.includes("png") ||
        fileType.includes("gif")
      ) {
        return "image";
      } else if (
        fileType.includes("video") ||
        fileType.includes("mp4") ||
        fileType.includes("avi")
      ) {
        return "video";
      } else if (
        fileType.includes("audio") ||
        fileType.includes("mp3") ||
        fileType.includes("wav")
      ) {
        return "audio";
      } else if (
        fileType.includes("zip") ||
        fileType.includes("rar") ||
        fileType.includes("archive")
      ) {
        return "archive";
      } else if (
        fileType.includes("code") ||
        fileType.includes("js") ||
        fileType.includes("ts") ||
        fileType.includes("py")
      ) {
        return "code";
      } else if (
        fileType.includes("database") ||
        fileType.includes("sql") ||
        fileType.includes("db")
      ) {
        return "database";
      } else if (
        fileType.includes("pdf") ||
        fileType.includes("doc") ||
        fileType.includes("txt")
      ) {
        return "document";
      }
      return "file";
    }
    return "default";
  };

  // Filter documents based on category selection
  const filteredDocuments =
    categoryFilter === "all"
      ? uiDocuments
      : uiDocuments.filter((document) => {
          const dataType = getDataType(document, "name");
          return dataType === categoryFilter;
        });

  // Categories for filtering based on document type
  const categories = [
    {
      key: "image",
      label: "Images",
      count: uiDocuments.filter((doc) => getDataType(doc, "name") === "image")
        .length,
    },
    {
      key: "document",
      label: "Documents",
      count: uiDocuments.filter(
        (doc) => getDataType(doc, "name") === "document"
      ).length,
    },
    {
      key: "archive",
      label: "Archives",
      count: uiDocuments.filter((doc) => getDataType(doc, "name") === "archive")
        .length,
    },
    {
      key: "default",
      label: "Other Files",
      count: uiDocuments.filter((doc) => getDataType(doc, "name") === "default")
        .length,
    },
  ];

  // Quick filters for advanced layout
  const quickFilters = [
    {
      label: "All Documents",
      value: "all",
      count: uiDocuments.length,
      active: searchTerm === "",
      onClick: () => setSearchTerm(""),
    },
    {
      label: "Images",
      value: "images",
      count: uiDocuments.filter((doc) => getDataType(doc, "name") === "image")
        .length,
      active: false,
      onClick: () => setSearchTerm("image"),
    },
    {
      label: "Documents",
      value: "documents",
      count: uiDocuments.filter(
        (doc) => getDataType(doc, "name") === "document"
      ).length,
      active: false,
      onClick: () => setSearchTerm("pdf"),
    },
    {
      label: "Archives",
      value: "archives",
      count: uiDocuments.filter((doc) => getDataType(doc, "name") === "archive")
        .length,
      active: false,
      onClick: () => setSearchTerm("zip"),
    },
  ];

  // Insights widgets for advanced layout
  const insightsWidgets: InsightsWidget[] = [
    {
      id: "total-documents",
      title: "Total Documents",
      value: statistics?.totalDocuments || 0,
      description: "All documents in the system",
      icon: <FileText size={16} className="text-blue-500" />,
      trend: {
        value: 12,
        isPositive: true,
      },
    },
    {
      id: "recent-uploads",
      title: "Recent Uploads",
      value: statistics?.recentUploads || 0,
      description: "Documents uploaded this week",
      icon: <FileText size={16} className="text-green-500" />,
      trend: {
        value: 8,
        isPositive: true,
      },
    },
    {
      id: "storage-used",
      title: "Storage Used",
      value: statistics?.storageUsed || "0 MB",
      description: "Total storage consumed",
      icon: <FileText size={16} className="text-orange-500" />,
    },
    {
      id: "file-types",
      title: "File Types",
      value: new Set(uiDocuments.map((doc) => doc.file_type)).size,
      description: "Different file formats",
      icon: <FileText size={16} className="text-purple-500" />,
    },
  ];

  // Context menu actions for flow view
  const contextMenuActions = [
    {
      id: "open",
      label: "Open Document",
      icon: <LottieIconPlayer icon={LottieIconLib.view} size={16} />,
      onClick: (item: Document) => handleOpenDocument(item),
    },
    {
      id: "download",
      label: "Download",
      icon: <LottieIconPlayer icon={LottieIconLib.download} size={16} />,
      onClick: (item: Document) => handleDownloadDocument(item),
    },
    {
      id: "separator1",
      label: "",
      separator: true,
      onClick: () => {},
    },
    {
      id: "edit",
      label: "Edit",
      icon: <LottieIconPlayer icon={LottieIconLib.edit} size={16} />,
      onClick: (item: Document) => handleEditDocumentDialog(item),
    },
    {
      id: "copy",
      label: "Make a Copy",
      icon: <LottieIconPlayer icon={LottieIconLib.copy} size={16} />,
      onClick: (item: Document) => handleCopyDocument(item),
    },
    {
      id: "separator2",
      label: "",
      separator: true,
      onClick: () => {},
    },
    {
      id: "delete",
      label: "Delete",
      icon: (
        <LottieIconPlayer
          icon={LottieIconLib.delete}
          size={16}
          className="text-red-500"
        />
      ),
      onClick: (item: Document) => handleDeleteDocument(item.id),
    },
  ];

  // Table columns configuration
  const columns = [
    {
      key: "name",
      label: "Name",
      dataType: "file" as DataType,
    },
    {
      key: "file_type",
      label: "Type",
    },
    {
      key: "size",
      label: "Size",
      render: (item: Document) => {
        if (!item.size) return "-";
        const bytes = parseInt(item.size);
        if (bytes === 0) return "0 B";
        const k = 1024;
        const sizes = ["B", "KB", "MB", "GB"];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
      },
    },
    {
      key: "category",
      label: "Category",
    },
    {
      key: "createdAt",
      label: "Created",
      render: (item: Document) => new Date(item.createdAt).toLocaleDateString(),
    },
  ];

  return (
    <Listing
      className="space-y-3"
      toolbar={
        isAdvanced
          ? {
              searchValue: searchTerm,
              onSearchChange: handleSearch,
              onSearchCommit: handleSearchCommit,
              searchPlaceholder: "Search documents...",
              quickFilters,
              viewMode: advancedViewMode,
              onViewModeChange: setAdvancedViewMode,
              enabledViewModes: ["table", "grid", "flow"],
              showInsightsButton: true,
              insightsWidgets,
            }
          : undefined
      }
    >
      {/* Basic Layout Components */}
      {!isAdvanced && (
        <>
          <DocumentHeader
            onCreateDocument={() => setIsCreateDialogOpen(true)}
            documentsCount={documents.length}
          />

          <DocumentStatisticsComponent
            statistics={statistics}
            isLoading={isLoading}
          />

          <Listing.Filters
            searchTerm={searchTerm}
            onSearchChange={handleSearch}
            enableDynamicFilters={true}
            columns={columns.map((col) => ({
              key: col.key,
              label: col.label,
              searchable: true,
              type:
                col.key === "size"
                  ? "number"
                  : col.key === "createdAt"
                  ? "date"
                  : "string",
            }))}
            tableData={filteredDocuments}
            enableControls={true}
            controlsViewMode={controlsViewMode}
            onControlsViewModeChange={setControlsViewMode}
            categoryFilter={categoryFilter}
            onCategoryFilterChange={setCategoryFilter}
            categories={categories}
            defaultFilterColumn="name"
            autoSelectDefaultColumn={true}
            onRefresh={handleRefresh}
            loading={isTableLoading}
            enableViewModeControls={true}
            viewMode={basicViewMode}
            onViewModeChange={setBasicViewMode}
            controlsActions={
              <RBACWrapper
                entity={DEFAULT_ENTITIES.CONTRACT}
                action={PERMISSION_ACTIONS.CREATE}
              >
                <Button
                  variant="secondary"
                  onClick={() => setIsCreateDialogOpen(true)}
                >
                  <LottieIconPlayer
                    icon={LottieIconLib.upload}
                    size={16}
                    className="mr-2"
                  />
                  Upload Document
                </Button>
              </RBACWrapper>
            }
          />
        </>
      )}

      {/* Advanced Layout Header */}
      {isAdvanced && (
        <Listing.Header
          title="Documents"
          caption={`Manage your project documents and files, ${
            documents.length
          } ${documents.length === 1 ? "document" : "documents"}`}
          actions={
            <RBACWrapper
              entity={DEFAULT_ENTITIES.DOCUMENT}
              action={PERMISSION_ACTIONS.CREATE}
            >
              <Button
                variant="secondary"
                onClick={() => setIsCreateDialogOpen(true)}
              >
                <LottieIconPlayer
                  icon={LottieIconLib.add}
                  size={16}
                  className="mr-2"
                />
                Upload New Document
              </Button>
            </RBACWrapper>
          }
        />
      )}

      {/* Advanced Layout - Flow View Only */}
      {isAdvanced && advancedViewMode === "flow" && (
        <Listing.Flow
          data={filteredDocuments}
          loading={isTableLoading}
          getDataType={(item) => getDataType(item, "name")}
          contextMenuActions={contextMenuActions}
          emptyState={
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                No documents
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Get started by uploading your first document.
              </p>
            </div>
          }
        />
      )}

      {/* Unified Data Renderer - Handles Table/Cards switching globally */}
      {((isAdvanced && advancedViewMode !== "flow") || !isAdvanced) && (
        <Listing.Table
          id="documents-table"
          data={filteredDocuments}
          columns={columns}
          loading={isTableLoading}
          viewMode={basicViewMode}
          displayMode={
            isAdvanced
              ? advancedViewMode === "grid"
                ? "cards"
                : "table"
              : controlsViewMode
          }
          getDataType={getDataType}
          renderCard={renderDocumentCard}
          contextMenuActions={contextMenuActions}
          emptyState={
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                No documents
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Get started by uploading your first document.
              </p>
            </div>
          }
        />
      )}

      {/* Create Dialog */}
      <DocumentCreateDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onCreateDocument={handleCreateDocument}
        isLoading={isAnyLoading}
      />

      {/* Edit Dialog */}
      {selectedDocument && (
        <DocumentEditDialog
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          document={selectedDocument}
          onUpdateDocument={handleEditSubmit}
          isLoading={isAnyLoading}
        />
      )}

      {/* Delete Dialog */}
      {selectedDocument && (
        <DocumentDeleteDialog
          open={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          document={selectedDocument}
          onDeleteDocument={handleDeleteConfirm}
          isLoading={isAnyLoading}
        />
      )}
    </Listing>
  );
}
