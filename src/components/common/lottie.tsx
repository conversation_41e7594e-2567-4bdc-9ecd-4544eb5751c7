"use client";

import { useState, useRef, useEffect } from "react";
import { cn } from "@/lib/utils";
import { Player } from "@lordicon/react";

// Lottie Icons
const Category = require("@/components/lordicons/category.json");
const PictureIcon = require("@/components/lordicons/picture.json");
const BasicLayout = require("@/components/lordicons/basic-layout.json");
const AdvancedLayout = require("@/components/lordicons/advanced-layout.json");
const MenuIcon = require("@/components/lordicons/menu.json");
const ViewIcon = require("@/components/lordicons/view.json");
const ViewIcon2 = require("@/components/lordicons/27-view.json");
const FolderIcon = require("@/components/lordicons/folder.json");
const AddFolderIcon = require("@/components/lordicons/add-folder.json");
const SquareIcon = require("@/components/lordicons/squares.json");
const ShoppingBagIcon = require("@/components/lordicons/shopping-bag.json");
const FingerPrintIcon = require("@/components/lordicons/fingerprint.json");
const ArrowSmallIcon = require("@/components/lordicons/arrow-up.json");
const ArrowSmallUpIcon = require("@/components/lordicons/arrow-small-up.json");
const ArrowRightIcon = require("@/components/lordicons/arrow-right.json");
const LoginIcon = require("@/components/lordicons/login.json");
const LogoutIcon = require("@/components/lordicons/logout.json");
const AddIcon = require("@/components/lordicons/add.json");
const ChatIcon = require("@/components/lordicons/chat.json");
const ContractIcon = require("@/components/lordicons/contract.json");
const CopyIcon = require("@/components/lordicons/copy.json");
const DeleteIcon = require("@/components/lordicons/delete.json");
const DownloadIcon = require("@/components/lordicons/download.json");
const RefreshIcon = require("@/components/lordicons/refresh.json");
const EditIcon = require("@/components/lordicons/edit.json");
const ElectricIcon = require("@/components/lordicons/electric.json");
const ExitIcon = require("@/components/lordicons/exit.json");
const FileIcon = require("@/components/lordicons/file.json");
const FilterIcon = require("@/components/lordicons/filter.json");
const FreeHandIcon = require("@/components/lordicons/free-hand.json");
const HomeIcon = require("@/components/lordicons/home.json");
const IncreaseIcon = require("@/components/lordicons/increase.json");
const InsightsIcon = require("@/components/lordicons/insights.json");
const LinkIcon = require("@/components/lordicons/link.json");
const ListIcon = require("@/components/lordicons/list.json");
const PaymentIcon = require("@/components/lordicons/payment.json");
const SendEmailIcon = require("@/components/lordicons/send-email.json");
const SettingsIcon = require("@/components/lordicons/settings.json");
const ShieldIcon = require("@/components/lordicons/shield.json");
const RedirectIcon = require("@/components/lordicons/redirect.json");
const UploadIcon = require("@/components/lordicons/upload.json");
const UploadCompleteIcon = require("@/components/lordicons/upload-complete.json");

export enum LottieIconLib {
  view = "view",
  view2 = "view2",
  folder = "folder",
  addFolder = "addFolder",
  basicLayout = "basicLayout",
  advancedLayout = "advancedLayout",
  squares = "squares",
  category = "category",
  shoppingBag = "shoppingBag",
  fingerprint = "fingerprint",
  arrowUp = "arrowUp",
  arrowSmallUp = "arrowSmallUp",
  arrowRight = "arrowRight",
  picture = "picture",
  login = "login",
  logout = "logout",
  menu = "menu",
  add = "add",
  chat = "chat",
  contract = "contract",
  refresh = "refresh",
  copy = "copy",
  delete = "delete",
  download = "download",
  edit = "edit",
  electric = "electric",
  exit = "exit",
  file = "file",
  filter = "filter",
  freeHand = "freeHand",
  home = "home",
  increase = "increase",
  insights = "insights",
  link = "link",
  redirect = "redirect",
  list = "list",
  payment = "payment",
  sendEmail = "sendEmail",
  settings = "settings",
  shield = "shield",
  upload = "upload",
  uploadComplete = "uploadComplete",
}

export function LottieIconPlayer({
  icon,
  highlightOnHover = false,
  className,
  onClick,
  size = 25,
}: {
  icon: any;
  className?: string;
  highlightOnHover?: boolean;
  onClick?: () => void;
  size?: number;
}) {
  const [highlight, setHighlight] = useState<string>("#ffffffde");

  const wrapperRef = useRef<HTMLSpanElement>(null);
  const playerRef = useRef<Player>(null);

  const onPlayPress = () => {
    playerRef.current?.playFromBeginning();
  };

  const actions = {
    MouseEnter: () => {
      if (highlightOnHover) {
        setHighlight("#00E58A");
      }
      onPlayPress();
    },
    MouseLeave: () => {
      setHighlight("#ffffffde");
    },
  };

  const IconLibrary: Record<string, any> = {
    view: ViewIcon,
    view2: ViewIcon2,
    folder: FolderIcon,
    addFolder: AddFolderIcon,
    basicLayout: BasicLayout,
    advancedLayout: AdvancedLayout,
    category: Category,
    squares: SquareIcon,
    picture: PictureIcon,
    shoppingBag: ShoppingBagIcon,
    fingerprint: FingerPrintIcon,
    arrowUp: ArrowSmallIcon,
    arrowSmallUp: ArrowSmallUpIcon,
    arrowRight: ArrowRightIcon,
    redirect: RedirectIcon,
    login: LoginIcon,
    logout: LogoutIcon,
    add: AddIcon,
    menu: MenuIcon,
    chat: ChatIcon,
    contract: ContractIcon,
    refresh: RefreshIcon,
    copy: CopyIcon,
    delete: DeleteIcon,
    download: DownloadIcon,
    edit: EditIcon,
    electric: ElectricIcon,
    exit: ExitIcon,
    file: FileIcon,
    filter: FilterIcon,
    freeHand: FreeHandIcon,
    home: HomeIcon,
    increase: IncreaseIcon,
    insights: InsightsIcon,
    link: LinkIcon,
    list: ListIcon,
    payment: PaymentIcon,
    sendEmail: SendEmailIcon,
    settings: SettingsIcon,
    shield: ShieldIcon,
    upload: UploadIcon,
    uploadComplete: UploadCompleteIcon,
  };

  if (!IconLibrary[icon]) return <></>;

  function ListenToParentComponentOnHoverEvent() {
    //
    const parentElem = wrapperRef.current?.parentElement;
    if (!parentElem) return;
    parentElem.addEventListener("mouseenter", actions.MouseEnter);
    parentElem.addEventListener("mouseleave", actions.MouseLeave);
  }

  useEffect(() => {
    ListenToParentComponentOnHoverEvent();
  }, []);

  return (
    <span
      ref={wrapperRef}
      onClick={onClick}
      onMouseEnter={onPlayPress}
      className={cn("w-max h-max cursor-pointer", className)}
    >
      <Player
        ref={playerRef}
        icon={IconLibrary[icon]}
        size={size}
        colorize={highlight}
      />
    </span>
  );
}
