"use client";

import { useEffect, useState } from "react";

import { useRouter } from "next/navigation";
import { Brand } from "../logo";
import { motion } from "framer-motion";
import Link from "next/link";
import { useAuth } from "@/hooks/useAuth";
import { cn } from "@/lib/utils";
import { BiLogoUpwork as Upwork } from "react-icons/bi";

import {
  FaLinkedinIn as Linkedin,
  FaInstagram as Instagram,
} from "react-icons/fa";

import type { MenubarProps, MenubtnProps, BarProps } from "./types";

import { LottieIconPlayer, LottieIconLib } from "../lottie";

export const glassTheme =
  "bg-black/[.5] border border-zinc-800 backdrop-blur-md";

export const Navbar = () => {
  return (
    <nav className="top-bar">
      <Link href="/" className="w-full max-w-md">
        <Brand />
      </Link>
    </nav>
  );
};

export const Menubar = ({ active }: MenubarProps) => {
  const [submenu, setShowSubMenu] = useState(false);

  const transition = {
    duration: 0.5,
    ease: "easeInOut",
  };

  // Sub-components
  const Menubtn = ({
    name = "",
    url = "#",
    icon,
    type = "link",
    style = "",
  }: MenubtnProps) => {
    const [hover, setHover] = useState(false);
    const [isLink, setIsLink] = useState(true);

    const globalstyle = `capitalize flex h-full flex-row gap-3 items-center justify-center ${style}`;
    transition["ease"] = "backOut";

    useEffect(() => {
      setIsLink(type !== "menu");
    }, [type]);

    return type !== "menu" ? (
      <Link
        href={"/" + url || "#"}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
        className={globalstyle}
      >
        {icon}
        <motion.p
          initial={{ width: 0, height: 0 }}
          animate={isLink && hover ? "show" : "hide"}
          variants={{
            show: {
              maxWidth: "72px",
              width: "100%",
              maxHeight: "20px",
              height: "100%",
            },
            hide: { width: 0, height: 0 },
          }}
          transition={transition}
          className="navbar-caption text-sm font-normal overflow-hidden origin-left"
        >
          {name}
        </motion.p>
      </Link>
    ) : (
      <motion.span
        initial={{ scale: 1 }}
        animate={hover ? "pop" : "normal"}
        variants={{
          pop: { scale: 1.28 },
          normal: { scale: 1 },
        }}
        transition={transition}
        onMouseEnter={() => setHover(true)}
        onMouseLeave={() => setHover(false)}
        className={globalstyle}
        onClick={() => setShowSubMenu(!submenu)}
      >
        {icon}
      </motion.span>
    );
  };

  const Menu = () => {
    const style = {
      start: "pr-3",
      center: "px-3",
      end: "pl-3 pr-1 hover:text-lime-200",
    };

    return (
      <span className="w-max flex flex-row items-center justify-center">
        {[
          {
            icon: (
              <Brand size="sm" style="hover:animate-pulse hover:bg-lime-200" />
            ),
            url: "",
            style: style.start,
            type: "link" as const,
          },
          {
            name: "projects",
            url: "projects",
            icon: (
              <LottieIconPlayer
                icon={LottieIconLib.folder}
                highlightOnHover={true}
              />
            ),
            style: style.center,
            type: "link" as const,
          },
          {
            name: "services",
            url: "services",
            icon: (
              <LottieIconPlayer
                icon={LottieIconLib.view2}
                highlightOnHover={true}
              />
            ),
            style: style.center,
            type: "link" as const,
          },
          {
            name: "market",
            url: "market",
            icon: (
              <LottieIconPlayer
                icon={LottieIconLib.shoppingBag}
                highlightOnHover={true}
              />
            ),
            style: style.center,
            type: "link" as const,
          },
          {
            icon: (
              <LottieIconPlayer
                icon={LottieIconLib.menu}
                highlightOnHover={true}
              />
            ),
            style: style.end,
            type: "menu" as const,
          },
        ]?.map((link, index) => {
          return <Menubtn key={index} {...link} />;
        })}
      </span>
    );
  };

  const Submenu = () => {
    return (
      <span className="w-max flex flex-row items-center justify-center py-2">
        {[
          {
            name: "follow",
            url: "https://www.instagram.com/underscor.io",
            icon: <Instagram size={15} />,
          },
          {
            name: "connect",
            url: "https://www.linkedin.com/company/underscor-io/?viewAsMember=true",
            icon: <Linkedin size={15} />,
            style: "px-4",
          },

          {
            name: "hire us",
            url: "https://www.upwork.com/agencies/1655373505334222848",
            icon: <Upwork size={20} />,
          },
        ]?.map((link, index) => {
          return <Menubtn key={index} {...link} />;
        })}
      </span>
    );
  };

  const Bar = ({ props, children, style = glassTheme }: BarProps) => {
    const id = props?.id ?? "";
    const toggle = props?.toggle ?? false;

    const initialStates: Record<string, any> = {
      submenu: {
        scaleY: 0,
      },
      menu: {
        width: "auto",
        height: "3rem",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        scaleY: 1,
      },
    };

    return (
      <motion.span
        initial={initialStates[id]}
        animate={
          toggle
            ? {
                scaleY: 1,
              }
            : {
                scaleY: 0,
              }
        }
        transition={{
          duration: 0.2,
          ease: "backIn",
        }}
        className={cn(`w-max origin-bottom px-4 rounded-lg`, style)}
      >
        {children}
      </motion.span>
    );
  };

  const MorphBar = () => {
    return (
      <span className="w-max h-max flex flex-col items-end gap-2">
        <Bar props={{ id: "submenu", toggle: submenu }}>
          <Submenu />
        </Bar>

        <Bar props={{ id: "menu", toggle: active }}>
          <Menu />
        </Bar>
      </span>
    );
  };

  const AuthenticationIcon = () => {
    const router = useRouter();
    const { isAuthenticated, logout } = useAuth();

    if (isAuthenticated) {
      return (
        <LottieIconPlayer
          icon={LottieIconLib.logout}
          onClick={() => logout()}
          highlightOnHover={true}
        />
      );
    }

    return (
      <LottieIconPlayer
        icon={LottieIconLib.login}
        onClick={() => router.push("/signin")}
        highlightOnHover={true}
      />
    );
  };

  return (
    <motion.nav
      animate={active ? "show" : "hide"}
      variants={{
        show: {
          bottom: 20,
        },
        hide: {
          bottom: -100,
        },
      }}
      transition={transition}
      className="z-50 fixed left-[50%] -translate-x-[50%] flex flex-row items-end justify-center gap-1 lg:gap-2 xl:gap-4"
    >
      <Bar
        props={{ id: "login", toggle: active }}
        style={`${glassTheme} py-[.65rem] hover:text-lime-250 duration-300`}
      >
        <AuthenticationIcon />
      </Bar>
      <MorphBar />
      <Bar
        props={{ id: "reset-scroll", toggle: active }}
        style={`${glassTheme} py-[.65rem]`}
      >
        <LottieIconPlayer
          icon={LottieIconLib.arrowUp}
          highlightOnHover={true}
        />
      </Bar>
    </motion.nav>
  );
};
