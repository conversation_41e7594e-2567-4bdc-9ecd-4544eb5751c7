/**
 * AI Chat Component - Example usage of the AI library
 *
 * This component demonstrates how to use the useAI hook for
 * both streaming and non-streaming AI responses.
 */

"use client";

import React, { useState } from "react";
import { useAI } from "@/hooks/useAI";

interface AIChatProps {
  className?: string;
  defaultModel?: string;
  systemPrompt?: string;
}

export function AIChat({
  className = "",
  defaultModel = "claude-3-5-sonnet-4.1",
  systemPrompt = "You are a helpful AI assistant.",
}: AIChatProps) {
  const [prompt, setPrompt] = useState("");
  const [useStreaming, setUseStreaming] = useState(false);

  const {
    isLoading,
    isStreaming,
    response,
    error,
    streamContent,
    generateResponse,
    streamResponse,
    clearResponse,
  } = useAI({
    model: defaultModel,
    systemPrompt,
    temperature: 0.7,
    maxTokens: 1000,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!prompt.trim()) return;

    if (useStreaming) {
      await streamResponse(prompt);
    } else {
      await generateResponse(prompt);
    }
  };

  const handleClear = () => {
    clearResponse();
    setPrompt("");
  };

  return (
    <div className={`ai-chat ${className}`}>
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            AI Chat Demo
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Powered by Vercel AI SDK - {defaultModel}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label
              htmlFor="prompt"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
            >
              Your Message
            </label>
            <textarea
              id="prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Ask me anything..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              rows={4}
              disabled={isLoading || isStreaming}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={useStreaming}
                  onChange={(e) => setUseStreaming(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  disabled={isLoading || isStreaming}
                />
                <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Enable Streaming
                </span>
              </label>
            </div>

            <div className="flex space-x-2">
              <button
                type="button"
                onClick={handleClear}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:bg-gray-600 dark:text-gray-300 dark:border-gray-500 dark:hover:bg-gray-700"
                disabled={isLoading || isStreaming}
              >
                Clear
              </button>
              <button
                type="submit"
                disabled={!prompt.trim() || isLoading || isStreaming}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading || isStreaming ? "Generating..." : "Send"}
              </button>
            </div>
          </div>
        </form>

        {/* Response Display */}
        {(response || streamContent || error) && (
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">
              Response
            </h3>

            {error && (
              <div className="text-red-600 dark:text-red-400">
                <p className="font-medium">Error: {error.error}</p>
                {error.message && (
                  <p className="text-sm mt-1">{error.message}</p>
                )}
              </div>
            )}

            {isStreaming && streamContent && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="animate-pulse w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    Streaming...
                  </span>
                </div>
                <div className="prose dark:prose-invert max-w-none">
                  <p className="whitespace-pre-wrap">{streamContent}</p>
                </div>
              </div>
            )}

            {response && !isStreaming && (
              <div className="space-y-3">
                <div className="prose dark:prose-invert max-w-none">
                  <p className="whitespace-pre-wrap">{response.content}</p>
                </div>

                <div className="text-xs text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-600 pt-2">
                  <div className="flex justify-between items-center">
                    <span>Model: {response.model}</span>
                    <span>
                      {new Date(response.timestamp).toLocaleTimeString()}
                    </span>
                  </div>

                  {response.usage && (
                    <div className="mt-1">
                      Tokens: {response.usage.promptTokens} prompt +{" "}
                      {response.usage.completionTokens} completion ={" "}
                      {response.usage.totalTokens} total
                    </div>
                  )}

                  {response.mock && (
                    <div className="mt-1 text-yellow-600 dark:text-yellow-400">
                      ⚠️ This is a mock response. Install AI provider packages
                      for real responses.
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Setup Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
            Setup Instructions
          </h4>
          <div className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
            <p>To enable real AI responses, install provider packages:</p>
            <code className="block bg-blue-100 dark:bg-blue-800 p-2 rounded text-xs">
              npm install @ai-sdk/openai @ai-sdk/anthropic
            </code>
            <p>Then configure your API keys in environment variables:</p>
            <code className="block bg-blue-100 dark:bg-blue-800 p-2 rounded text-xs">
              OPENAI_API_KEY=your_gpt5_key_here
              <br />
              ANTHROPIC_API_KEY=your_claude_key_here
            </code>
            <p className="text-sm mt-2">
              <strong>Available Models:</strong> GPT-5 and Claude Sonnet 4.1
              only
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
