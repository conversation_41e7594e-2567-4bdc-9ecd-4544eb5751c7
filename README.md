# Common PWA Template

A comprehensive Next.js Progressive Web App template with built-in common features for rapid application development.

**Author & Maintainer:** Underscore International Company Limited

## 🚀 Features

This template includes pre-built, production-ready modules for:

- **Multi-Channel Notifications** - Email, push notifications, and in-app notifications
- **REST API Services** - Structured API layer with validation and error handling
- **Theme Management** - Light/dark theme support with system preference detection
- **Authentication & Authorization** - Complete auth system with role-based access control (RBAC)
- **Real-time Communication** - Socket.io integration for live updates
- **Payment Processing** - Braintree integration for secure payments
- **File Management** - Document upload, preview, and management system
- **Service Worker** - PWA capabilities with offline support
- **State Management** - Redux Toolkit with persistence
- **UI Components** - Comprehensive component library with shadcn/ui

## 📁 Project Structure

```
src/
├── app/                    # Next.js app router pages
├── components/             # Reusable UI components
│   ├── common/            # Shared components
│   ├── view/              # Page-specific components
│   └── ai/                # AI-related components
├── hooks/                 # Custom React hooks
├── lib/                   # Utility libraries and services
│   ├── api/               # API services and validators
│   ├── auth.ts            # Authentication configuration
│   ├── notifications/     # Notification system
│   ├── rbac/              # Role-based access control
│   └── service-worker/    # PWA service worker utilities
├── providers/             # React context providers
├── store/                 # Redux store configuration
├── types/                 # TypeScript type definitions
└── modules/               # Reusable modules and utilities
```

## 🛠 Core Modules

### [Authentication & Authorization](./docs/auth.md)

Complete authentication system with OAuth, credentials, and RBAC.

### [Multi-Channel Notifications](./docs/notifications.md)

Unified notification system supporting email, push, and in-app notifications.

### [REST API Services](./docs/api-services.md)

Structured API layer with validation, error handling, and service patterns.

### [Theme Management](./docs/theme.md)

Comprehensive theming system with light/dark mode support.

### [Real-time Communication](./docs/realtime.md)

Socket.io integration for live updates and real-time features.

### [Payment Processing](./docs/payments.md)

Secure payment processing with Braintree integration.

### [File Management](./docs/file-management.md)

Document upload, storage, and preview system.

### [Service Worker & PWA](./docs/pwa.md)

Progressive Web App capabilities with offline support.

## 🚀 Quick Start

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd common-pwa-nextjs
   ```

2. **Install dependencies**

   ```bash
   pnpm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Set up the database**

   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Start the development server**
   ```bash
   pnpm dev
   ```

## 📚 Documentation

- [Getting Started](./docs/getting-started.md)
- [Configuration](./docs/configuration.md)
- [Deployment](./docs/deployment.md)
- [Contributing](./docs/contributing.md)

## 🔧 Environment Variables

Key environment variables you need to configure:

```env
# Database
DATABASE_URL="your-database-url"

# Authentication
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="http://localhost:3030"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Notifications
NEXT_PUBLIC_VAPID_PUBLIC_KEY="your-vapid-public-key"
VAPID_PRIVATE_KEY="your-vapid-private-key"

# Email
SMTP_HOST="your-smtp-host"
SMTP_PORT="587"
SMTP_USER="your-smtp-user"
SMTP_PASS="your-smtp-password"

# Payments
BRAINTREE_MERCHANT_ID="your-braintree-merchant-id"
BRAINTREE_PUBLIC_KEY="your-braintree-public-key"
BRAINTREE_PRIVATE_KEY="your-braintree-private-key"
```

## 🏗 Built With

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **State Management**: Redux Toolkit
- **Database**: Prisma ORM
- **Authentication**: NextAuth.js
- **Payments**: Braintree
- **Real-time**: Socket.io
- **Notifications**: Web Push API, Nodemailer
- **Icons**: Lucide React, Lottie

## 👥 Author & Maintainer

**Underscore International Company Limited**

- Website: [underscore.international](https://underscor.io)
- Email: <EMAIL>

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🤝 Contributing

Please read [CONTRIBUTING.md](./docs/contributing.md) for details on our code of conduct and the process for submitting pull requests.

## 📞 Support

For support and questions, please refer to the [documentation](./docs/) or create an issue in the repository.

---

© 2024 Underscore International Company Limited. All rights reserved.
